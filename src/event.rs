#[derive(Debu<PERSON>, <PERSON><PERSON>)]
pub struct SolanaEvent {
    pub mint: String, // Mint address as a string
    pub sol_liquidity: f64, // Amount of SOL liquidity in the pool
    pub base_mint: String,
    pub quote_mint: String,
    pub base_amount: f64,
    pub quote_amount: f64,
    pub sol_reserves: f64, // SOL reserves in the pool
    pub token_reserves: f64, // Token reserves in the pool
    pub creator: String, // Optional creator address
    pub market_cap: f64,
    pub start_price: f64,
    pub dev_entity_id: i32,
}


