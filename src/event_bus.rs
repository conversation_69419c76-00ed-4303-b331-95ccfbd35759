use once_cell::sync::Lazy;
use tokio::sync::{mpsc, Mutex};
use tokio::task;
use crate::event::EventType;
use crate::db::{
    create_dev_entity, create_dev_token_creation, create_dev_wallet, get_dev_entity,
    get_dev_token_creation, get_dev_wallet, update_dev_entity_field,
    update_dev_token_creation_field, update_dev_wallet_field, DevEntity, DevTokenCreation,
    DevWallet,
};

use crate::event::SolanaEvent;

pub static EVENT_BUS: Lazy<Mutex<Option<mpsc::Sender<SolanaEvent>>>> =
    Lazy::new(|| Mutex::new(None));

pub async fn init_event_bus() {
    let (tx, mut rx) = mpsc::channel(100);

    task::spawn(async move {
        while let Some(event) = rx.recv().await {
            handle_event(event).await;
        }
    });

    let mut bus = EVENT_BUS.lock().await;
    *bus = Some(tx);
}

pub async fn emit_event(event: SolanaEvent) {
    if let Some(sender) = EVENT_BUS.lock().await.as_ref() {
        let _ = sender.send(event).await;
    } else {
        eprintln!("❌ Event bus not initialized");
    }
}

async fn handle_event(event: SolanaEvent) {
    println!("🟢 Handling event: {:?}", event);

    let current_time = chrono::Local::now()
        .naive_local()
        .format("%Y-%m-%d %H:%M:%S")
        .to_string();

    //check if dev in table
    // if not create dev_entity and wallet
    //then create dev_token_creation

    // Check if dev wallet exists

    match event.event_type {
        EventType::CreatePool => {
            let wallet_exists = get_dev_wallet(&event.creator.as_ref().unwrap()).unwrap();
            let mut entity_id: i32 = 0;

            if wallet_exists.is_none() {
                // Create new dev entity
                let dev_entity = DevEntity {
                    id: None,
                    label: event.creator.clone(),
                    score: None,
                    note: Some(String::new()),
                    max_hold: None,
                    max_pct: None,
                    max_sol: None,
                    created_at: None,
                };

                entity_id = create_dev_entity(&dev_entity).unwrap();

                // Create new dev wallet
                let dev_wallet = DevWallet {
                    id: None,
                    address: event.creator.clone().unwrap(),
                    parent_wallet: "".to_string(),
                    dev_entity_id: entity_id.clone(),
                    created_at: None,
                    scan: Some(1),
                };

                create_dev_wallet(&dev_wallet).unwrap();
            } else {
                // get walle entinty id from devWallet
                entity_id = wallet_exists.unwrap().dev_entity_id;
            }

            let mut dev_entity_id: i32;
            if event.dev_entity_id.is_none() {
                dev_entity_id = entity_id;
            } else {
                dev_entity_id = event.dev_entity_id.unwrap();
            }

            let dev_creation: DevTokenCreation = DevTokenCreation {
                id: None,
                dev_entity_id: dev_entity_id,
                creator: event.creator.unwrap_or_default(),
                base_mint: event.base_mint.unwrap_or_default(),
                quote_mint: event.quote_mint.unwrap_or_default(),
                base_amount: event.base_amount.unwrap_or_default(),
                quote_amount: event.quote_amount.unwrap_or_default(),
                sol_liquidity: event.sol_liquidity.unwrap_or_default(),
                tx_amount: None,
                volume: None,
                ath_mcap: None,
                ath_at: None,
                ath_pct: None,
                withdraw_at: None,
                withdraw_pct: None,
                created_at: None,
                is_running: Some(1),
                start_price: event.start_price.unwrap_or_default(),
                start_mcap: event.market_cap.unwrap_or_default(),
            };

            if let Err(e) = create_dev_token_creation(&dev_creation) {
                eprintln!("❌ Failed to create dev token creation: {:?}", e);
            }
        }
        EventType::Buy => {
            println!("Buy");
        }
        EventType::Sell => {
            println!("Sell");
        }
        EventType::Withdraw => {
            println!("Withdraw");
        }
    }
}
