use once_cell::sync::Lazy;
use tokio::sync::{mpsc, Mutex};
use tokio::task;
use crate::event::EventType;
use crate::db::{
    create_dev_entity, create_dev_token_creation, create_dev_wallet, update_dev_token_creation, get_dev_token_creation, get_dev_wallet, update_dev_entity_field, update_dev_token_creation_field, update_dev_wallet, update_dev_wallet_field, DevEntity, DevTokenCreation, DevWallet
};

use crate::trends::{get_mint_data, list_all_mints};

use crate::grpc::SOL_PRICE;

use crate::event::SolanaEvent;


pub fn get_mc(base_mint: &str,base_amount : f64, quote_amount: f64) -> f64 {

    
    if base_mint == "So11111111111111111111111111111111111111112" {
        base_amount/ quote_amount   * SOL_PRICE * 1_000_000_000.0
    } else {
        quote_amount/ base_amount  * SOL_PRICE * 1_000_000_000.0
    }

}

pub static EVENT_BUS: Lazy<Mutex<Option<mpsc::Sender<SolanaEvent>>>> =
    Lazy::new(|| Mutex::new(None));

pub async fn init_event_bus() {
    let (tx, mut rx) = mpsc::channel(100);

    task::spawn(async move {
        while let Some(event) = rx.recv().await {
            handle_event(event).await;
        }
    });

    let mut bus = EVENT_BUS.lock().await;
    *bus = Some(tx);
}

pub async fn emit_event(event: SolanaEvent) {
    if let Some(sender) = EVENT_BUS.lock().await.as_ref() {
        let _ = sender.send(event).await;
    } else {
        eprintln!("❌ Event bus not initialized");
    }
}

async fn handle_event(event: SolanaEvent) {
    

    let current_time = chrono::Local::now()
        .naive_local()
        .format("%Y-%m-%d %H:%M:%S")
        .to_string();

    //check if dev in table
    // if not create dev_entity and wallet
    //then create dev_token_creation

    // Check if dev wallet exists

    match event.event_type {
        EventType::CreatePool => {
            println!("🟢 Handling CreateEvent: {:?}", event);
            let wallet_exists = get_dev_wallet(&event.creator.as_ref().unwrap()).unwrap();
            let mut entity_id: i32 = 0;

            if wallet_exists.is_none() {
                // Create new dev entity
                let dev_entity = DevEntity {
                    id: None,
                    label: event.creator.clone(),
                    score: None,
                    note: Some(String::new()),
                    max_hold: None,
                    max_pct: None,
                    max_sol: None,
                    created_at: None,
                };

                entity_id = create_dev_entity(&dev_entity).unwrap();
                println!("New Entity ID: {}", entity_id);

                // Create new dev wallet
                let dev_wallet = DevWallet {
                    id: None,
                    address: event.creator.clone().unwrap(),
                    parent_wallet: "".to_string(),
                    dev_entity_id: entity_id.clone(),
                    created_at: None,
                    scan: Some(0),
                };

                create_dev_wallet(&dev_wallet).unwrap();
            } else {
                // get walle entinty id from devWallet
                entity_id = wallet_exists.clone().unwrap().dev_entity_id;
                println!("Existing Entity ID: {}", entity_id);
                if let Some(mut wallet) = wallet_exists {
                    wallet.scan = Some(0);
                    update_dev_wallet(&wallet).unwrap();
                }

            }

            println!("Entity ID: {}", entity_id);

            let dev_creation: DevTokenCreation = DevTokenCreation {
                id: None,
                dev_entity_id: entity_id,
                mint : event.mint.unwrap_or_default(),
                creator: event.creator.unwrap_or_default(),
                base_mint: event.base_mint.unwrap_or_default(),
                quote_mint: event.quote_mint.unwrap_or_default(),
                base_amount: event.base_amount.unwrap_or_default(),
                quote_amount: event.quote_amount.unwrap_or_default(),
                sol_liquidity: event.sol_liquidity.unwrap_or_default(),
                tx_amount: None,
                volume: None,
                ath_mcap: None,
                ath_at: None,
                ath_pct: None,
                withdraw_at: None,
                withdraw_pct: None,
                withdraw_mcap: None,
                created_at: None,
                is_running: Some(1),
                start_price: event.start_price.unwrap_or_default(),
                start_mcap: event.market_cap.unwrap_or_default(),
            };

            if let Err(e) = create_dev_token_creation(&dev_creation) {
                eprintln!("❌ Failed to create dev token creation: {:?}", e);
            }
        }
        EventType::Buy => {
            println!("Buy");
        }
        EventType::Sell => {
            println!("Sell");
        }
        EventType::Withdraw => {
            println!("❌ Handling WITHDRAW Evebnt: {:?}", event);
            let wallet_exists = get_dev_wallet(&event.creator.as_ref().unwrap()).unwrap();
            
            // update wallet set scan=1 use with function update_dev_wallet
            if let Some(mut wallet) = wallet_exists {
                wallet.scan = Some(1);
                update_dev_wallet(&wallet).unwrap();,
            }
          
            let mint_data=get_mint_data(&event.mint.as_ref().unwrap()).unwrap();

          let dev_creation = get_dev_token_creation(&event.mint.as_ref().unwrap()).unwrap();
            if let Some(mut creation) = dev_creation {



                let mc = get_mc(&creation.base_mint, creation.base_amount, creation.quote_amount);

                creation.withdraw_at = Some(chrono::Local::now().timestamp()).map(|ts| ts.to_string());
                creation.withdraw_pct = Some(0.0);
                creation.is_running = Some(0);
                creation.withdraw_mcap = Some(mc as f32);
                creation.ath_mcap = Some(mint_data.ath_mcap);
                update_dev_token_creation(&creation).unwrap();
            }

            

        }
    }
}
